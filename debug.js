#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔍 项目结构诊断...\n')

// 检查当前目录
console.log('📁 当前工作目录:', process.cwd())

// 检查根目录文件
console.log('\n📋 根目录文件:')
try {
  const rootFiles = fs.readdirSync('.')
  rootFiles.forEach(file => {
    const stat = fs.statSync(file)
    console.log(`  ${stat.isDirectory() ? '📁' : '📄'} ${file}`)
  })
} catch (error) {
  console.log('  ❌ 无法读取根目录:', error.message)
}

// 检查frontend目录
console.log('\n📱 Frontend 目录:')
if (fs.existsSync('frontend')) {
  try {
    const frontendFiles = fs.readdirSync('frontend')
    frontendFiles.forEach(file => {
      const stat = fs.statSync(path.join('frontend', file))
      console.log(`  ${stat.isDirectory() ? '📁' : '📄'} ${file}`)
    })
    
    // 检查package.json
    if (fs.existsSync('frontend/package.json')) {
      console.log('\n📦 Frontend package.json:')
      const pkg = JSON.parse(fs.readFileSync('frontend/package.json', 'utf8'))
      console.log(`  名称: ${pkg.name}`)
      console.log(`  版本: ${pkg.version}`)
      console.log('  脚本:')
      Object.keys(pkg.scripts || {}).forEach(script => {
        console.log(`    ${script}: ${pkg.scripts[script]}`)
      })
    }
    
    // 检查node_modules
    if (fs.existsSync('frontend/node_modules')) {
      console.log('\n✅ Frontend node_modules 存在')
    } else {
      console.log('\n❌ Frontend node_modules 不存在')
    }
    
  } catch (error) {
    console.log('  ❌ 无法读取frontend目录:', error.message)
  }
} else {
  console.log('  ❌ frontend 目录不存在')
}

// 检查backend目录
console.log('\n⚡ Backend 目录:')
if (fs.existsSync('backend')) {
  try {
    const backendFiles = fs.readdirSync('backend')
    backendFiles.forEach(file => {
      const stat = fs.statSync(path.join('backend', file))
      console.log(`  ${stat.isDirectory() ? '📁' : '📄'} ${file}`)
    })
  } catch (error) {
    console.log('  ❌ 无法读取backend目录:', error.message)
  }
} else {
  console.log('  ❌ backend 目录不存在')
}

// 检查Node.js和npm版本
console.log('\n🔧 环境信息:')
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim()
  console.log(`  Node.js: ${nodeVersion}`)
} catch (error) {
  console.log('  ❌ 无法获取Node.js版本')
}

try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
  console.log(`  npm: ${npmVersion}`)
} catch (error) {
  console.log('  ❌ 无法获取npm版本')
}

console.log('\n💡 建议解决方案:')
console.log('1. 确保在项目根目录运行命令')
console.log('2. 安装frontend依赖: cd frontend && npm install')
console.log('3. 运行开发命令: npm run dev:mp-weixin')
console.log('4. 或直接在frontend目录运行: npm run dev:mp-weixin')
